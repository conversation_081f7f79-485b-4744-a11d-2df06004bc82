const fs = require('fs');
const path = require('path');

// 代码文件扩展名
const codeExtensions = [
  '.ts', '.js', '.tsx', '.jsx', '.vue', 
  '.py', '.java', '.cpp', '.c', '.h', '.cs', 
  '.go', '.rs', '.php', '.rb', '.swift', '.kt', 
  '.scala', '.sol', '.md', '.json', '.yml', 
  '.yaml', '.xml', '.html', '.css', '.scss', '.less'
];

// 需要排除的目录
const excludeDirs = ['node_modules', 'dist', 'build', 'coverage', '.git', '.next', 'out'];

function shouldExclude(filePath) {
  return excludeDirs.some(dir => filePath.includes(dir));
}

function countLinesInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return content.split('\n').length;
  } catch (error) {
    return 0;
  }
}

function scanDirectory(dirPath, stats = { files: 0, lines: 0, byType: {} }) {
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      
      if (shouldExclude(fullPath)) {
        continue;
      }
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath, stats);
      } else if (stat.isFile()) {
        const ext = path.extname(item).toLowerCase();
        
        if (codeExtensions.includes(ext)) {
          const lines = countLinesInFile(fullPath);
          stats.files++;
          stats.lines += lines;
          
          if (!stats.byType[ext]) {
            stats.byType[ext] = { files: 0, lines: 0 };
          }
          stats.byType[ext].files++;
          stats.byType[ext].lines += lines;
        }
      }
    }
  } catch (error) {
    // 忽略无法访问的目录
  }
  
  return stats;
}

function countDirectory(dirPath, name) {
  if (!fs.existsSync(dirPath)) {
    console.log(`${name}: 目录不存在`);
    return { files: 0, lines: 0 };
  }
  
  const stats = scanDirectory(dirPath);
  console.log(`${name}: ${stats.files} 个文件, ${stats.lines} 行代码`);
  return stats;
}

console.log('=== DL引擎项目代码统计 ===\n');

// 统计各个主要目录
const directories = [
  ['engine', '引擎核心 (engine)'],
  ['editor', '编辑器 (editor)'],
  ['server', '服务端 (server)'],
  ['examples', '示例代码 (examples)'],
  ['tests', '测试代码 (tests)'],
  ['scripts', '脚本工具 (scripts)'],
  ['docs', '文档 (docs)'],
  ['contracts', '智能合约 (contracts)'],
  ['tools', '工具 (tools)'],
  ['config', '配置文件 (config)']
];

const allStats = { files: 0, lines: 0, byType: {} };

for (const [dir, name] of directories) {
  const stats = countDirectory(dir, name);
  allStats.files += stats.files;
  allStats.lines += stats.lines;
}

console.log('\n=== 总计统计 ===');
console.log(`总文件数: ${allStats.files}`);
console.log(`总代码行数: ${allStats.lines}`);

// 统计整个项目的文件类型分布
console.log('\n=== 按文件类型统计 ===');
const projectStats = scanDirectory('.');

const sortedTypes = Object.entries(projectStats.byType)
  .sort((a, b) => b[1].lines - a[1].lines);

for (const [ext, stats] of sortedTypes) {
  console.log(`${ext}: ${stats.files} 个文件, ${stats.lines} 行`);
}

console.log('\n=== 项目总计 ===');
console.log(`项目总文件数: ${projectStats.files}`);
console.log(`项目总代码行数: ${projectStats.lines}`);
