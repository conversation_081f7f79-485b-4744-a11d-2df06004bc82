/**
 * 路径动画映射器
 * 根据路径状态和上下文自动选择合适的动画
 */
import * as THREE from 'three';
import { Logger } from '../../utils/Logger';

/**
 * 动画映射规则
 */
export interface AnimationMappingRule {
  /** 规则名称 */
  name: string;
  /** 条件函数 */
  condition: (context: AnimationContext) => boolean;
  /** 动画名称 */
  animation: string;
  /** 优先级 */
  priority: number;
  /** 是否循环 */
  loop?: boolean;
  /** 动画速度倍数 */
  speedMultiplier?: number;
}

/**
 * 动画上下文
 */
export interface AnimationContext {
  /** 当前速度 */
  speed: number;
  /** 移动方向 */
  direction: THREE.Vector3;
  /** 是否在地面上 */
  isGrounded: boolean;
  /** 当前地形类型 */
  terrainType: string;
  /** 路径点类型 */
  waypointType?: string;
  /** 等待时间 */
  waitTime: number;
  /** 是否有朝向目标 */
  hasLookAtTarget: boolean;
  /** 当前动画 */
  currentAnimation: string;
  /** 自定义数据 */
  customData?: any;
}

/**
 * 路径动画映射器
 */
export class PathAnimationMapper {
  private logger = new Logger('PathAnimationMapper');
  private rules: AnimationMappingRule[] = [];
  private defaultAnimation = 'idle';

  /**
   * 构造函数
   */
  constructor() {
    this.initializeDefaultRules();
  }

  /**
   * 初始化默认规则
   */
  private initializeDefaultRules(): void {
    // 空闲状态
    this.addRule({
      name: 'idle',
      condition: (ctx) => ctx.speed <= 0.1 && ctx.waitTime > 0,
      animation: 'idle',
      priority: 1,
      loop: true
    });

    // 行走状态
    this.addRule({
      name: 'walk',
      condition: (ctx) => ctx.speed > 0.1 && ctx.speed <= 2.0 && ctx.isGrounded,
      animation: 'walk',
      priority: 2,
      loop: true,
      speedMultiplier: 1.0
    });

    // 跑步状态
    this.addRule({
      name: 'run',
      condition: (ctx) => ctx.speed > 2.0 && ctx.speed <= 6.0 && ctx.isGrounded,
      animation: 'run',
      priority: 3,
      loop: true,
      speedMultiplier: 1.2
    });

    // 冲刺状态
    this.addRule({
      name: 'sprint',
      condition: (ctx) => ctx.speed > 6.0 && ctx.isGrounded,
      animation: 'sprint',
      priority: 4,
      loop: true,
      speedMultiplier: 1.5
    });

    // 跳跃状态
    this.addRule({
      name: 'jump',
      condition: (ctx) => !ctx.isGrounded && ctx.direction.y > 0,
      animation: 'jump',
      priority: 10,
      loop: false
    });

    // 下落状态
    this.addRule({
      name: 'fall',
      condition: (ctx) => !ctx.isGrounded && ctx.direction.y < -0.5,
      animation: 'fall',
      priority: 9,
      loop: true
    });

    // 着陆状态
    this.addRule({
      name: 'land',
      condition: (ctx) => ctx.isGrounded && ctx.currentAnimation === 'fall',
      animation: 'land',
      priority: 8,
      loop: false
    });

    // 转身状态
    this.addRule({
      name: 'turn',
      condition: (ctx) => ctx.hasLookAtTarget && ctx.speed <= 0.5,
      animation: 'turn',
      priority: 5,
      loop: false
    });

    // 爬坡状态
    this.addRule({
      name: 'climb',
      condition: (ctx) => ctx.terrainType === 'slope' && ctx.speed > 0.1,
      animation: 'climb',
      priority: 6,
      loop: true
    });

    // 游泳状态
    this.addRule({
      name: 'swim',
      condition: (ctx) => ctx.terrainType === 'water' && ctx.speed > 0.1,
      animation: 'swim',
      priority: 7,
      loop: true
    });

    // 特殊路径点动画
    this.addRule({
      name: 'waypoint_special',
      condition: (ctx) => ctx.waypointType === 'special' && ctx.waitTime > 0,
      animation: 'special_action',
      priority: 15,
      loop: false
    });

    // 对话状态
    this.addRule({
      name: 'talk',
      condition: (ctx) => ctx.waypointType === 'dialogue' && ctx.waitTime > 0,
      animation: 'talk',
      priority: 12,
      loop: true
    });

    // 挥手状态
    this.addRule({
      name: 'wave',
      condition: (ctx) => ctx.waypointType === 'greeting' && ctx.waitTime > 0,
      animation: 'wave',
      priority: 11,
      loop: false
    });

    this.logger.info('默认动画映射规则初始化完成', { ruleCount: this.rules.length });
  }

  /**
   * 添加映射规则
   * @param rule 映射规则
   */
  public addRule(rule: AnimationMappingRule): void {
    if (!rule || typeof rule !== 'object') {
      this.logger.warn('无效的动画映射规则', { rule });
      return;
    }

    if (!rule.name || typeof rule.name !== 'string') {
      this.logger.warn('动画映射规则缺少有效名称', { rule });
      return;
    }

    if (!rule.animation || typeof rule.animation !== 'string') {
      this.logger.warn('动画映射规则缺少有效动画名称', { rule });
      return;
    }

    if (typeof rule.condition !== 'function') {
      this.logger.warn('动画映射规则缺少有效条件函数', { rule });
      return;
    }

    this.rules.push(rule);
    // 按优先级排序
    this.rules.sort((a, b) => b.priority - a.priority);

    this.logger.debug('添加动画映射规则', { name: rule.name, priority: rule.priority });
  }

  /**
   * 移除映射规则
   * @param name 规则名称
   */
  public removeRule(name: string): boolean {
    const index = this.rules.findIndex(rule => rule.name === name);
    if (index >= 0) {
      this.rules.splice(index, 1);
      this.logger.debug('移除动画映射规则', { name });
      return true;
    }
    return false;
  }

  /**
   * 获取匹配的动画
   * @param context 动画上下文
   * @returns 动画信息
   */
  public getAnimation(context: AnimationContext): {
    animation: string;
    loop: boolean;
    speedMultiplier: number;
    rule?: AnimationMappingRule;
  } {
    // 验证输入上下文
    if (!context || typeof context !== 'object') {
      this.logger.warn('无效的动画上下文', { context });
      return {
        animation: this.defaultAnimation,
        loop: true,
        speedMultiplier: 1.0
      };
    }

    // 查找匹配的规则
    for (const rule of this.rules) {
      try {
        if (rule && typeof rule.condition === 'function' && rule.condition(context)) {
          this.logger.debug('匹配动画规则', {
            rule: rule.name,
            animation: rule.animation,
            context: this.contextToString(context)
          });

          return {
            animation: rule.animation,
            loop: rule.loop ?? false,
            speedMultiplier: rule.speedMultiplier ?? 1.0,
            rule
          };
        }
      } catch (error) {
        this.logger.warn('动画规则条件评估失败', { rule: rule?.name || 'unknown', error });
      }
    }

    // 没有匹配的规则，返回默认动画
    this.logger.debug('使用默认动画', {
      animation: this.defaultAnimation,
      context: this.contextToString(context)
    });

    return {
      animation: this.defaultAnimation,
      loop: true,
      speedMultiplier: 1.0
    };
  }

  /**
   * 设置默认动画
   * @param animation 默认动画名称
   */
  public setDefaultAnimation(animation: string): void {
    this.defaultAnimation = animation;
    this.logger.info('设置默认动画', { animation });
  }

  /**
   * 获取所有规则
   * @returns 规则列表
   */
  public getRules(): AnimationMappingRule[] {
    return [...this.rules];
  }

  /**
   * 清除所有规则
   */
  public clearRules(): void {
    this.rules.length = 0;
    this.logger.info('清除所有动画映射规则');
  }

  /**
   * 重置为默认规则
   */
  public resetToDefaults(): void {
    this.clearRules();
    this.initializeDefaultRules();
    this.logger.info('重置为默认动画映射规则');
  }

  /**
   * 创建动画上下文
   * @param params 参数
   * @returns 动画上下文
   */
  public static createContext(params: {
    speed: number;
    direction: THREE.Vector3;
    isGrounded?: boolean;
    terrainType?: string;
    waypointType?: string;
    waitTime?: number;
    hasLookAtTarget?: boolean;
    currentAnimation?: string;
    customData?: any;
  }): AnimationContext {
    return {
      speed: params.speed,
      direction: params.direction.clone(),
      isGrounded: params.isGrounded ?? true,
      terrainType: params.terrainType ?? 'ground',
      waypointType: params.waypointType,
      waitTime: params.waitTime ?? 0,
      hasLookAtTarget: params.hasLookAtTarget ?? false,
      currentAnimation: params.currentAnimation ?? 'idle',
      customData: params.customData
    };
  }

  /**
   * 预定义的动画映射配置
   */
  public static readonly PRESETS = {
    /**
     * 基础配置
     */
    BASIC: [
      {
        name: 'idle',
        condition: (ctx: AnimationContext) => ctx.speed <= 0.1,
        animation: 'idle',
        priority: 1,
        loop: true
      },
      {
        name: 'walk',
        condition: (ctx: AnimationContext) => ctx.speed > 0.1 && ctx.speed <= 3.0,
        animation: 'walk',
        priority: 2,
        loop: true
      },
      {
        name: 'run',
        condition: (ctx: AnimationContext) => ctx.speed > 3.0,
        animation: 'run',
        priority: 3,
        loop: true
      }
    ],

    /**
     * 高级配置
     */
    ADVANCED: [
      // 基础配置
      {
        name: 'idle',
        condition: (ctx: AnimationContext) => ctx.speed <= 0.1,
        animation: 'idle',
        priority: 1,
        loop: true
      },
      {
        name: 'walk',
        condition: (ctx: AnimationContext) => ctx.speed > 0.1 && ctx.speed <= 3.0,
        animation: 'walk',
        priority: 2,
        loop: true
      },
      {
        name: 'run',
        condition: (ctx: AnimationContext) => ctx.speed > 3.0,
        animation: 'run',
        priority: 3,
        loop: true
      },
      {
        name: 'jump',
        condition: (ctx: AnimationContext) => !ctx.isGrounded,
        animation: 'jump',
        priority: 10,
        loop: false
      },
      {
        name: 'turn',
        condition: (ctx: AnimationContext) => ctx.hasLookAtTarget && ctx.speed <= 0.5,
        animation: 'turn',
        priority: 5,
        loop: false
      }
    ],

    /**
     * 社交配置
     */
    SOCIAL: [
      {
        name: 'talk',
        condition: (ctx: AnimationContext) => ctx.waypointType === 'dialogue',
        animation: 'talk',
        priority: 15,
        loop: true
      },
      {
        name: 'wave',
        condition: (ctx: AnimationContext) => ctx.waypointType === 'greeting',
        animation: 'wave',
        priority: 12,
        loop: false
      },
      {
        name: 'bow',
        condition: (ctx: AnimationContext) => ctx.waypointType === 'bow',
        animation: 'bow',
        priority: 11,
        loop: false
      }
    ]
  };

  /**
   * 应用预设配置
   * @param preset 预设名称
   */
  public applyPreset(preset: keyof typeof PathAnimationMapper.PRESETS): void {
    const rules = PathAnimationMapper.PRESETS[preset];
    if (rules && Array.isArray(rules)) {
      this.clearRules();
      rules.forEach(rule => this.addRule(rule as AnimationMappingRule));
      this.logger.info('应用动画映射预设', { preset, ruleCount: rules.length });
    } else {
      this.logger.warn('未知的动画映射预设', { preset });
    }
  }

  /**
   * 将上下文转换为字符串（用于调试）
   * @param context 动画上下文
   * @returns 字符串表示
   */
  private contextToString(context: AnimationContext): string {
    try {
      return `speed:${context.speed.toFixed(2)}, ` +
             `grounded:${context.isGrounded}, ` +
             `terrain:${context.terrainType}, ` +
             `waypoint:${context.waypointType || 'none'}, ` +
             `wait:${context.waitTime.toFixed(2)}`;
    } catch (error) {
      this.logger.warn('上下文字符串转换失败', { error });
      return 'invalid_context';
    }
  }
}
