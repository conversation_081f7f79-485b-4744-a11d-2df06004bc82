/**
 * 物理驱动的面部动画组件
 * 使用物理模拟来驱动面部动画，实现更自然的面部表情
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { FacialExpressionType } from './FacialAnimationComponent';
import { MuscleType, MuscleData } from '../systems/PhysicalFacialAnimationSystem';

/**
 * 物理肌肉
 */
export interface PhysicalMuscle extends MuscleData {
  /** 物理体 */
  bodies?: any[];
  /** 约束 */
  constraints?: any[];
  /** 当前力 */
  currentForce?: THREE.Vector3;
  /** 目标位置 */
  targetPosition?: THREE.Vector3;
  /** 初始位置 */
  initialPosition?: THREE.Vector3;
  /** 是否活动 */
  active?: boolean;
}

/**
 * 表情肌肉配置
 */
export interface ExpressionMuscleConfig {
  /** 肌肉名称 */
  muscleName: string;
  /** 力方向 */
  forceDirection: THREE.Vector3;
  /** 力大小 */
  forceMagnitude: number;
  /** 目标位置 */
  targetPosition?: THREE.Vector3;
  /** 刚度 */
  stiffness?: number;
  /** 阻尼 */
  damping?: number;
}

/**
 * 表情配置
 */
export interface ExpressionConfig {
  /** 表情类型 */
  expression: FacialExpressionType;
  /** 肌肉配置 */
  muscles: ExpressionMuscleConfig[];
}

/**
 * 物理驱动的面部动画组件
 */
export class PhysicalFacialAnimationComponent extends Component {
  /** 组件类型 */
  static readonly type = 'PhysicalFacialAnimation';

  /** 肌肉映射 */
  private muscles: Map<string, PhysicalMuscle> = new Map();

  /** 表情配置 */
  private expressionConfigs: Map<FacialExpressionType, ExpressionConfig> = new Map();

  /** 当前表情 */
  private _currentExpression: FacialExpressionType = FacialExpressionType.NEUTRAL;

  /** 表情权重 */
  private _expressionWeight: number = 0;

  /** 混合形状权重 */
  private blendShapeWeights: Map<string, number> = new Map();

  /** 物理体 */
  private bodies: any[] = [];

  /** 约束 */
  private constraints: any[] = [];

  /** 是否已初始化 */
  private initialized: boolean = false;



  /** 调试模式 */
  private debug: boolean = false;

  /**
   * 构造函数
   * @param entity 实体
   */
  constructor(entity: Entity) {
    super(PhysicalFacialAnimationComponent.type);
    this.setEntity(entity);

    // 初始化默认表情配置
    this.initDefaultExpressionConfigs();
  }

  /**
   * 初始化默认表情配置
   */
  private initDefaultExpressionConfigs(): void {
    // 中性表情
    this.expressionConfigs.set(FacialExpressionType.NEUTRAL, {
      expression: FacialExpressionType.NEUTRAL,
      muscles: []
    });

    // 开心表情
    this.expressionConfigs.set(FacialExpressionType.HAPPY, {
      expression: FacialExpressionType.HAPPY,
      muscles: [
        {
          muscleName: 'cheek_left',
          forceDirection: new THREE.Vector3(0, 0.05, 0),
          forceMagnitude: 2.0
        },
        {
          muscleName: 'cheek_right',
          forceDirection: new THREE.Vector3(0, 0.05, 0),
          forceMagnitude: 2.0
        },
        {
          muscleName: 'lip_upper',
          forceDirection: new THREE.Vector3(0, 0.03, 0),
          forceMagnitude: 1.5
        },
        {
          muscleName: 'lip_lower',
          forceDirection: new THREE.Vector3(0, -0.02, 0),
          forceMagnitude: 1.0
        }
      ]
    });

    // 悲伤表情
    this.expressionConfigs.set(FacialExpressionType.SAD, {
      expression: FacialExpressionType.SAD,
      muscles: [
        {
          muscleName: 'eyebrow_left',
          forceDirection: new THREE.Vector3(0, -0.02, 0),
          forceMagnitude: 1.5
        },
        {
          muscleName: 'eyebrow_right',
          forceDirection: new THREE.Vector3(0, -0.02, 0),
          forceMagnitude: 1.5
        },
        {
          muscleName: 'lip_upper',
          forceDirection: new THREE.Vector3(0, -0.01, 0),
          forceMagnitude: 0.8
        },
        {
          muscleName: 'lip_lower',
          forceDirection: new THREE.Vector3(0, -0.03, 0),
          forceMagnitude: 1.2
        }
      ]
    });

    // 愤怒表情
    this.expressionConfigs.set(FacialExpressionType.ANGRY, {
      expression: FacialExpressionType.ANGRY,
      muscles: [
        {
          muscleName: 'eyebrow_left',
          forceDirection: new THREE.Vector3(0.02, -0.01, 0),
          forceMagnitude: 2.0
        },
        {
          muscleName: 'eyebrow_right',
          forceDirection: new THREE.Vector3(-0.02, -0.01, 0),
          forceMagnitude: 2.0
        },
        {
          muscleName: 'jaw_center',
          forceDirection: new THREE.Vector3(0, -0.02, 0),
          forceMagnitude: 1.5
        }
      ]
    });

    // 惊讶表情
    this.expressionConfigs.set(FacialExpressionType.SURPRISED, {
      expression: FacialExpressionType.SURPRISED,
      muscles: [
        {
          muscleName: 'eyebrow_left',
          forceDirection: new THREE.Vector3(0, 0.03, 0),
          forceMagnitude: 2.0
        },
        {
          muscleName: 'eyebrow_right',
          forceDirection: new THREE.Vector3(0, 0.03, 0),
          forceMagnitude: 2.0
        },
        {
          muscleName: 'jaw_center',
          forceDirection: new THREE.Vector3(0, -0.04, 0),
          forceMagnitude: 2.5
        }
      ]
    });

    // 恐惧表情
    this.expressionConfigs.set(FacialExpressionType.FEARFUL, {
      expression: FacialExpressionType.FEARFUL,
      muscles: [
        {
          muscleName: 'eyebrow_left',
          forceDirection: new THREE.Vector3(0, 0.02, 0),
          forceMagnitude: 1.8
        },
        {
          muscleName: 'eyebrow_right',
          forceDirection: new THREE.Vector3(0, 0.02, 0),
          forceMagnitude: 1.8
        },
        {
          muscleName: 'lip_upper',
          forceDirection: new THREE.Vector3(0, 0.01, 0),
          forceMagnitude: 0.5
        },
        {
          muscleName: 'lip_lower',
          forceDirection: new THREE.Vector3(0, -0.01, 0),
          forceMagnitude: 0.5
        }
      ]
    });

    // 厌恶表情
    this.expressionConfigs.set(FacialExpressionType.DISGUSTED, {
      expression: FacialExpressionType.DISGUSTED,
      muscles: [
        {
          muscleName: 'cheek_left',
          forceDirection: new THREE.Vector3(0, 0.02, 0),
          forceMagnitude: 1.0
        },
        {
          muscleName: 'cheek_right',
          forceDirection: new THREE.Vector3(0, 0.02, 0),
          forceMagnitude: 1.0
        },
        {
          muscleName: 'lip_upper',
          forceDirection: new THREE.Vector3(0, 0.02, 0),
          forceMagnitude: 1.2
        }
      ]
    });

    // 鄙视表情
    this.expressionConfigs.set(FacialExpressionType.CONTEMPT, {
      expression: FacialExpressionType.CONTEMPT,
      muscles: [
        {
          muscleName: 'eyebrow_left',
          forceDirection: new THREE.Vector3(0, 0.01, 0),
          forceMagnitude: 0.8
        },
        {
          muscleName: 'cheek_right',
          forceDirection: new THREE.Vector3(0, 0.01, 0),
          forceMagnitude: 1.0
        },
        {
          muscleName: 'lip_upper',
          forceDirection: new THREE.Vector3(0.01, 0, 0),
          forceMagnitude: 0.5
        }
      ]
    });
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    if (this.initialized) return;

    // 初始化肌肉
    for (const muscle of this.muscles.values()) {
      this.initializeMuscle(muscle);
    }

    this.initialized = true;

    if (this.debug) {
      console.log('物理面部动画组件已初始化');
    }
  }

  /**
   * 初始化肌肉
   * @param muscle 肌肉
   */
  private initializeMuscle(muscle: PhysicalMuscle): void {
    // 保存初始位置
    muscle.initialPosition = muscle.end.clone();

    // 创建物理体
    const startBody = {
      position: muscle.start.clone(),
      velocity: new THREE.Vector3(),
      force: new THREE.Vector3(),
      mass: muscle.fixedStart ? Infinity : muscle.mass,
      radius: muscle.radius,
      isFixed: muscle.fixedStart
    };

    const endBody = {
      position: muscle.end.clone(),
      velocity: new THREE.Vector3(),
      force: new THREE.Vector3(),
      mass: muscle.fixedEnd ? Infinity : muscle.mass,
      radius: muscle.radius,
      isFixed: muscle.fixedEnd
    };

    // 创建约束
    const constraint = {
      bodyA: startBody,
      bodyB: endBody,
      restLength: muscle.restLength || muscle.start.distanceTo(muscle.end),
      stiffness: muscle.stiffness,
      damping: muscle.damping,
      update: (_deltaTime: number) => {
        if (startBody.isFixed && endBody.isFixed) return;

        // 计算当前长度
        const direction = new THREE.Vector3().subVectors(endBody.position, startBody.position);
        const currentLength = direction.length();

        // 计算方向单位向量
        direction.normalize();

        // 计算力大小
        const forceMagnitude = (currentLength - constraint.restLength) * constraint.stiffness;

        // 计算力
        const force = direction.multiplyScalar(forceMagnitude);

        // 应用力
        if (!startBody.isFixed) {
          startBody.force.add(force);
        }

        if (!endBody.isFixed) {
          endBody.force.sub(force);
        }
      }
    };

    // 保存物理体和约束
    muscle.bodies = [startBody, endBody];
    muscle.constraints = [constraint];
    muscle.currentForce = new THREE.Vector3();
    muscle.active = true;

    // 添加到全局列表
    this.bodies.push(startBody, endBody);
    this.constraints.push(constraint);
  }

  /**
   * 添加肌肉
   * @param muscleData 肌肉数据
   * @returns 是否成功添加
   */
  public addMuscle(muscleData: MuscleData): boolean {
    // 检查是否已存在同名肌肉
    if (this.muscles.has(muscleData.name)) {
      console.warn(`肌肉 ${muscleData.name} 已存在`);
      return false;
    }

    // 创建肌肉
    const muscle: PhysicalMuscle = {
      ...muscleData,
      currentForce: new THREE.Vector3(),
      active: true
    };

    // 添加到映射
    this.muscles.set(muscleData.name, muscle);

    // 如果已初始化，则初始化肌肉
    if (this.initialized) {
      this.initializeMuscle(muscle);
    }

    return true;
  }

  /**
   * 应用表情
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public applyExpression(expression: FacialExpressionType, weight: number = 1.0): boolean {
    // 检查表情配置是否存在
    if (!this.expressionConfigs.has(expression)) {
      console.warn(`表情配置 ${expression} 不存在`);
      return false;
    }

    // 保存当前表情和权重
    this._currentExpression = expression;
    this._expressionWeight = weight;

    // 重置所有肌肉力
    for (const muscle of this.muscles.values()) {
      muscle.currentForce?.set(0, 0, 0);
    }

    // 如果是中性表情或权重为0，则不应用力
    if (expression === FacialExpressionType.NEUTRAL || weight <= 0) {
      return true;
    }

    // 获取表情配置
    const config = this.expressionConfigs.get(expression)!;

    // 应用肌肉力
    for (const muscleConfig of config.muscles) {
      // 获取肌肉
      const muscle = this.muscles.get(muscleConfig.muscleName);
      if (!muscle) continue;

      // 计算力
      const force = muscleConfig.forceDirection.clone().multiplyScalar(muscleConfig.forceMagnitude * weight);

      // 应用力
      this.applyMuscleForce(muscleConfig.muscleName, force);

      // 设置目标位置（如果有）
      if (muscleConfig.targetPosition) {
        muscle.targetPosition = muscleConfig.targetPosition.clone();
      }

      // 设置刚度和阻尼（如果有）
      if (muscleConfig.stiffness !== undefined) {
        this.setMuscleStiffness(muscleConfig.muscleName, muscleConfig.stiffness);
      }

      if (muscleConfig.damping !== undefined) {
        this.setMuscleDamping(muscleConfig.muscleName, muscleConfig.damping);
      }
    }

    return true;
  }

  /**
   * 应用肌肉力
   * @param muscleName 肌肉名称
   * @param force 力
   * @returns 是否成功应用
   */
  public applyMuscleForce(muscleName: string, force: THREE.Vector3): boolean {
    // 获取肌肉
    const muscle = this.muscles.get(muscleName);
    if (!muscle) {
      console.warn(`肌肉 ${muscleName} 不存在`);
      return false;
    }

    // 保存当前力
    muscle.currentForce?.copy(force);

    // 如果已初始化，则应用力
    if (this.initialized && muscle.bodies) {
      // 应用力到末端物理体
      const endBody = muscle.bodies[1];
      if (!endBody.isFixed) {
        endBody.force.add(force);
      }
    }

    return true;
  }

  /**
   * 设置肌肉刚度
   * @param muscleName 肌肉名称
   * @param stiffness 刚度
   * @returns 是否成功设置
   */
  public setMuscleStiffness(muscleName: string, stiffness: number): boolean {
    // 获取肌肉
    const muscle = this.muscles.get(muscleName);
    if (!muscle) {
      console.warn(`肌肉 ${muscleName} 不存在`);
      return false;
    }

    // 更新肌肉刚度
    muscle.stiffness = stiffness;

    // 如果已初始化，则更新约束
    if (this.initialized && muscle.constraints) {
      for (const constraint of muscle.constraints) {
        constraint.stiffness = stiffness;
      }
    }

    return true;
  }

  /**
   * 设置肌肉阻尼
   * @param muscleName 肌肉名称
   * @param damping 阻尼
   * @returns 是否成功设置
   */
  public setMuscleDamping(muscleName: string, damping: number): boolean {
    // 获取肌肉
    const muscle = this.muscles.get(muscleName);
    if (!muscle) {
      console.warn(`肌肉 ${muscleName} 不存在`);
      return false;
    }

    // 更新肌肉阻尼
    muscle.damping = damping;

    // 如果已初始化，则更新约束
    if (this.initialized && muscle.constraints) {
      for (const constraint of muscle.constraints) {
        constraint.damping = damping;
      }
    }

    return true;
  }

  /**
   * 重置所有肌肉
   * @returns 是否成功重置
   */
  public resetAllMuscles(): boolean {
    // 重置所有肌肉力
    for (const muscle of this.muscles.values()) {
      muscle.currentForce?.set(0, 0, 0);

      // 如果已初始化，则重置物理体
      if (this.initialized && muscle.bodies) {
        const endBody = muscle.bodies[1];
        if (!endBody.isFixed) {
          // 重置位置
          endBody.position.copy(muscle.initialPosition || muscle.end);
          // 重置速度
          endBody.velocity.set(0, 0, 0);
          // 重置力
          endBody.force.set(0, 0, 0);
        }
      }
    }

    // 重置表情
    this._currentExpression = FacialExpressionType.NEUTRAL;
    this._expressionWeight = 0;

    return true;
  }

  /**
   * 获取当前表情
   * @returns 当前表情类型
   */
  public getCurrentExpression(): FacialExpressionType {
    return this._currentExpression;
  }

  /**
   * 获取表情权重
   * @returns 表情权重
   */
  public getExpressionWeight(): number {
    return this._expressionWeight;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    if (!this.isEnabled() || !this.initialized) return;

    // 更新混合形状权重
    this.updateBlendShapeWeights();
  }

  /**
   * 更新混合形状权重
   */
  private updateBlendShapeWeights(): void {
    // 清空混合形状权重
    this.blendShapeWeights.clear();

    // 根据肌肉位置计算混合形状权重
    for (const muscle of this.muscles.values()) {
      if (!muscle.bodies) continue;

      const endBody = muscle.bodies[1];
      const initialPosition = muscle.initialPosition || muscle.end;

      // 计算位移
      const displacement = new THREE.Vector3().subVectors(endBody.position, initialPosition);

      // 根据肌肉类型和位移计算混合形状权重
      switch (muscle.type) {
        case MuscleType.JAW:
          // 下巴下移
          if (displacement.y < 0) {
            this.blendShapeWeights.set('jawOpen', Math.min(1.0, Math.abs(displacement.y) * 20));
          }
          break;

        case MuscleType.CHEEK:
          // 脸颊上移
          if (displacement.y > 0) {
            const side = muscle.name.includes('left') ? 'Left' : 'Right';
            this.blendShapeWeights.set(`cheek${side}Raise`, Math.min(1.0, displacement.y * 30));
          }
          break;

        case MuscleType.EYEBROW:
          // 眉毛上移或下移
          const side = muscle.name.includes('left') ? 'Left' : 'Right';
          if (displacement.y > 0) {
            this.blendShapeWeights.set(`eyebrow${side}Up`, Math.min(1.0, displacement.y * 40));
          } else if (displacement.y < 0) {
            this.blendShapeWeights.set(`eyebrow${side}Down`, Math.min(1.0, Math.abs(displacement.y) * 40));
          }
          break;

        case MuscleType.LIP:
          // 嘴唇移动
          if (muscle.name.includes('upper')) {
            if (displacement.y > 0) {
              this.blendShapeWeights.set('mouthUpperUp', Math.min(1.0, displacement.y * 30));
            }
          } else if (muscle.name.includes('lower')) {
            if (displacement.y < 0) {
              this.blendShapeWeights.set('mouthLowerDown', Math.min(1.0, Math.abs(displacement.y) * 30));
            }
          }
          break;
      }
    }
  }

  /**
   * 获取混合形状权重
   * @returns 混合形状权重映射
   */
  public getBlendShapeWeights(): Map<string, number> {
    return this.blendShapeWeights;
  }

  /**
   * 获取肌肉
   * @returns 肌肉数组
   */
  public getMuscles(): PhysicalMuscle[] {
    return Array.from(this.muscles.values());
  }

  /**
   * 获取物理体
   * @returns 物理体数组
   */
  public getBodies(): any[] {
    return this.bodies;
  }

  /**
   * 获取约束
   * @returns 约束数组
   */
  public getConstraints(): any[] {
    return this.constraints;
  }



  /**
   * 设置调试模式
   * @param debug 是否启用调试
   */
  public setDebug(debug: boolean): void {
    this.debug = debug;
  }

  /**
   * 获取调试模式
   * @returns 是否启用调试
   */
  public isDebug(): boolean {
    return this.debug;
  }
}
