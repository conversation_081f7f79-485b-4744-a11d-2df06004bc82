/**
 * PathAnimationMapper 测试用例
 */
import { describe, test, expect, beforeEach } from 'vitest';
import * as THREE from 'three';
import { PathAnimationMapper, AnimationMappingRule } from '../../src/avatar/utils/PathAnimationMapper';

describe('PathAnimationMapper', () => {
  let mapper: PathAnimationMapper;

  beforeEach(() => {
    mapper = new PathAnimationMapper();
  });

  describe('基础功能测试', () => {
    test('应该正确初始化默认规则', () => {
      const rules = mapper.getRules();
      expect(rules.length).toBeGreaterThan(0);
      expect(rules.some(rule => rule.name === 'idle')).toBe(true);
      expect(rules.some(rule => rule.name === 'walk')).toBe(true);
      expect(rules.some(rule => rule.name === 'run')).toBe(true);
    });

    test('应该能够添加自定义规则', () => {
      const customRule: AnimationMappingRule = {
        name: 'custom',
        condition: (ctx) => ctx.speed > 10,
        animation: 'custom_animation',
        priority: 100
      };

      mapper.addRule(customRule);
      const rules = mapper.getRules();
      expect(rules.some(rule => rule.name === 'custom')).toBe(true);
    });

    test('应该能够移除规则', () => {
      const result = mapper.removeRule('idle');
      expect(result).toBe(true);
      
      const rules = mapper.getRules();
      expect(rules.some(rule => rule.name === 'idle')).toBe(false);
    });
  });

  describe('动画匹配测试', () => {
    test('应该为空闲状态返回正确动画', () => {
      const context = PathAnimationMapper.createContext({
        speed: 0,
        direction: new THREE.Vector3(0, 0, 0),
        waitTime: 1
      });

      const result = mapper.getAnimation(context);
      expect(result.animation).toBe('idle');
      expect(result.loop).toBe(true);
    });

    test('应该为行走状态返回正确动画', () => {
      const context = PathAnimationMapper.createContext({
        speed: 1.5,
        direction: new THREE.Vector3(1, 0, 0),
        isGrounded: true
      });

      const result = mapper.getAnimation(context);
      expect(result.animation).toBe('walk');
      expect(result.loop).toBe(true);
    });

    test('应该为跑步状态返回正确动画', () => {
      const context = PathAnimationMapper.createContext({
        speed: 4.0,
        direction: new THREE.Vector3(1, 0, 0),
        isGrounded: true
      });

      const result = mapper.getAnimation(context);
      expect(result.animation).toBe('run');
      expect(result.loop).toBe(true);
    });
  });

  describe('错误处理测试', () => {
    test('应该处理无效的规则输入', () => {
      // 测试空规则
      mapper.addRule(null as any);
      mapper.addRule(undefined as any);
      
      // 测试缺少必要字段的规则
      mapper.addRule({} as any);
      mapper.addRule({ name: 'test' } as any);
      
      // 规则数量不应该增加
      const initialRuleCount = mapper.getRules().length;
      expect(mapper.getRules().length).toBe(initialRuleCount);
    });

    test('应该处理无效的上下文输入', () => {
      const result1 = mapper.getAnimation(null as any);
      expect(result1.animation).toBe('idle');
      
      const result2 = mapper.getAnimation(undefined as any);
      expect(result2.animation).toBe('idle');
      
      const result3 = mapper.getAnimation({} as any);
      expect(result3.animation).toBe('idle');
    });

    test('应该处理规则条件函数异常', () => {
      const faultyRule: AnimationMappingRule = {
        name: 'faulty',
        condition: () => { throw new Error('Test error'); },
        animation: 'faulty_animation',
        priority: 999
      };

      mapper.addRule(faultyRule);
      
      const context = PathAnimationMapper.createContext({
        speed: 1,
        direction: new THREE.Vector3(1, 0, 0)
      });

      const result = mapper.getAnimation(context);
      // 应该跳过有问题的规则，返回其他匹配的规则或默认动画
      expect(result.animation).not.toBe('faulty_animation');
    });
  });

  describe('预设配置测试', () => {
    test('应该能够应用基础预设', () => {
      mapper.applyPreset('BASIC');
      const rules = mapper.getRules();
      expect(rules.length).toBeGreaterThan(0);
      expect(rules.some(rule => rule.name === 'idle')).toBe(true);
    });

    test('应该能够应用高级预设', () => {
      mapper.applyPreset('ADVANCED');
      const rules = mapper.getRules();
      expect(rules.length).toBeGreaterThan(0);
      expect(rules.some(rule => rule.name === 'jump')).toBe(true);
    });

    test('应该能够应用社交预设', () => {
      mapper.applyPreset('SOCIAL');
      const rules = mapper.getRules();
      expect(rules.length).toBeGreaterThan(0);
      expect(rules.some(rule => rule.name === 'talk')).toBe(true);
    });
  });

  describe('工具方法测试', () => {
    test('createContext 应该正确创建上下文', () => {
      const direction = new THREE.Vector3(1, 0, 0);
      const context = PathAnimationMapper.createContext({
        speed: 2.0,
        direction: direction
      });

      expect(context.speed).toBe(2.0);
      expect(context.direction).toEqual(direction);
      expect(context.isGrounded).toBe(true);
      expect(context.terrainType).toBe('ground');
      expect(context.waitTime).toBe(0);
      expect(context.hasLookAtTarget).toBe(false);
      expect(context.currentAnimation).toBe('idle');
    });

    test('应该能够设置和获取默认动画', () => {
      mapper.setDefaultAnimation('custom_idle');
      
      const context = PathAnimationMapper.createContext({
        speed: 0,
        direction: new THREE.Vector3(0, 0, 0)
      });

      // 清除所有规则，强制使用默认动画
      mapper.clearRules();
      const result = mapper.getAnimation(context);
      expect(result.animation).toBe('custom_idle');
    });
  });
});
