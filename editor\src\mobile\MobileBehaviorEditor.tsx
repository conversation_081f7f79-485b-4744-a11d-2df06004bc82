/**
 * 移动端行为编辑器
 * 
 * 专为移动设备优化的行为树编辑器，具有以下特性：
 * - 触摸友好的界面设计
 * - 手势操作支持
 * - 响应式布局
 * - 离线编辑支持
 * - 云同步功能
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  PanGestureHandler,
  PinchGestureHandler,
  TapGestureHandler,
  State,
  Dimensions,
  Alert,
  Modal,
  TextInput,
  Switch
} from 'react-native';
import { Svg, Circle, Line, Rect, Text as SvgText } from 'react-native-svg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-netinfo/netinfo';
import {
  BehaviorNodeType,
  BehaviorNodeStatus
} from '../../../engine/src/ai/behavior/BehaviorTreeEngine';
import { MobileBehaviorEngine } from '../../../engine/src/mobile/MobileBehaviorEngine';

/**
 * 移动端节点配置
 */
interface MobileNodeConfig {
  id: string;
  name: string;
  type: BehaviorNodeType;
  position: { x: number; y: number };
  size: { width: number; height: number };
  properties: { [key: string]: any };
  children: string[];
  parent?: string;
  isSelected: boolean;
  isCollapsed: boolean;
}

/**
 * 手势状态
 */
interface GestureState {
  scale: number;
  translateX: number;
  translateY: number;
  lastScale: number;
  lastTranslateX: number;
  lastTranslateY: number;
}

/**
 * 编辑器状态
 */
interface EditorState {
  nodes: { [key: string]: MobileNodeConfig };
  selectedNodeId: string | null;
  isEditing: boolean;
  isOnline: boolean;
  lastSyncTime: number;
  isDirty: boolean;
}

/**
 * 移动端行为编辑器组件
 */
const MobileBehaviorEditor: React.FC<{
  entityId?: string;
  onSave?: (config: any) => void;
  onLoad?: () => any;
}> = ({ entityId, onSave, onLoad }) => {
  // 屏幕尺寸
  const screenData = Dimensions.get('window');
  const [screenDimensions, setScreenDimensions] = useState(screenData);
  
  // 编辑器状态
  const [editorState, setEditorState] = useState<EditorState>({
    nodes: {},
    selectedNodeId: null,
    isEditing: false,
    isOnline: true,
    lastSyncTime: 0,
    isDirty: false
  });
  
  // 手势状态
  const [gestureState, setGestureState] = useState<GestureState>({
    scale: 1,
    translateX: 0,
    translateY: 0,
    lastScale: 1,
    lastTranslateX: 0,
    lastTranslateY: 0
  });
  
  // 界面状态
  const [showNodePalette, setShowNodePalette] = useState(false);
  const [showProperties, setShowProperties] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  
  // 引用
  const canvasRef = useRef<View>(null);
  const panRef = useRef<PanGestureHandler>(null);
  const pinchRef = useRef<PinchGestureHandler>(null);
  
  // 移动端引擎
  const [mobileEngine] = useState(() => new MobileBehaviorEngine({
    platform: 'ios', // 或 'android'
    screenSize: screenDimensions,
    pixelRatio: 2,
    memoryLimit: 2048,
    cpuCores: 4,
    batteryLevel: 1.0,
    networkType: 'wifi',
    orientation: 'portrait',
    isLowPowerMode: false
  }));

  /**
   * 初始化组件
   */
  useEffect(() => {
    initializeEditor();
    setupNetworkListener();
    setupDimensionListener();
    
    return () => {
      cleanup();
    };
  }, []);

  /**
   * 初始化编辑器
   */
  const initializeEditor = async () => {
    try {
      // 加载本地数据
      const savedData = await loadLocalData();
      if (savedData) {
        setEditorState(prev => ({
          ...prev,
          nodes: savedData.nodes || {},
          lastSyncTime: savedData.lastSyncTime || 0
        }));
      }
      
      // 创建默认节点
      if (Object.keys(savedData?.nodes || {}).length === 0) {
        createDefaultTree();
      }
      
    } catch (error) {
      console.error('编辑器初始化失败:', error);
    }
  };

  /**
   * 设置网络监听
   */
  const setupNetworkListener = () => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setEditorState(prev => ({
        ...prev,
        isOnline: state.isConnected || false
      }));
      
      // 网络恢复时自动同步
      if (state.isConnected && editorState.isDirty) {
        syncToCloud();
      }
    });
    
    return unsubscribe;
  };

  /**
   * 设置屏幕尺寸监听
   */
  const setupDimensionListener = () => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenDimensions(window);
      
      // 更新移动端引擎设备信息
      mobileEngine.updateDeviceInfo({
        screenSize: window,
        orientation: window.width > window.height ? 'landscape' : 'portrait'
      });
    });
    
    return () => subscription?.remove();
  };

  /**
   * 创建默认行为树
   */
  const createDefaultTree = () => {
    const rootNode: MobileNodeConfig = {
      id: 'root',
      name: '根节点',
      type: BehaviorNodeType.SELECTOR,
      position: { x: screenDimensions.width / 2, y: 100 },
      size: { width: 120, height: 60 },
      properties: {},
      children: [],
      isSelected: false,
      isCollapsed: false
    };
    
    setEditorState(prev => ({
      ...prev,
      nodes: { root: rootNode },
      isDirty: true
    }));
  };

  /**
   * 处理平移手势
   */
  const handlePanGesture = useCallback((event: any) => {
    const { translationX, translationY, state } = event.nativeEvent;
    
    if (state === State.ACTIVE) {
      setGestureState(prev => ({
        ...prev,
        translateX: prev.lastTranslateX + translationX,
        translateY: prev.lastTranslateY + translationY
      }));
    } else if (state === State.END) {
      setGestureState(prev => ({
        ...prev,
        lastTranslateX: prev.translateX,
        lastTranslateY: prev.translateY
      }));
    }
  }, []);

  /**
   * 处理缩放手势
   */
  const handlePinchGesture = useCallback((event: any) => {
    const { scale, state } = event.nativeEvent;

    if (state === State.ACTIVE) {
      setGestureState(prev => {
        const newScale = Math.max(0.5, Math.min(3, prev.lastScale * scale));
        return {
          ...prev,
          scale: newScale
        };
      });
    } else if (state === State.END) {
      setGestureState(prev => ({
        ...prev,
        lastScale: prev.scale
      }));
    }
  }, []);

  /**
   * 处理节点点击
   */
  const handleNodeTap = useCallback((nodeId: string) => {
    setEditorState(prev => ({
      ...prev,
      selectedNodeId: prev.selectedNodeId === nodeId ? null : nodeId,
      nodes: Object.fromEntries(
        Object.entries(prev.nodes).map(([id, node]) => [
          id,
          { ...node, isSelected: id === nodeId }
        ])
      )
    }));
  }, []);

  /**
   * 添加节点
   */
  const addNode = useCallback((type: BehaviorNodeType, position: { x: number; y: number }) => {
    const nodeId = `node_${Date.now()}`;
    const newNode: MobileNodeConfig = {
      id: nodeId,
      name: getNodeTypeName(type),
      type,
      position,
      size: { width: 100, height: 50 },
      properties: {},
      children: [],
      isSelected: false,
      isCollapsed: false
    };
    
    setEditorState(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: newNode
      },
      isDirty: true
    }));
    
    // 自动保存到本地
    saveLocalData();
  }, []);

  /**
   * 删除节点
   */
  const deleteNode = useCallback((nodeId: string) => {
    Alert.alert(
      '确认删除',
      '确定要删除这个节点吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            setEditorState(prev => {
              const newNodes = { ...prev.nodes };
              delete newNodes[nodeId];
              
              // 移除父子关系
              Object.values(newNodes).forEach(node => {
                node.children = node.children.filter(id => id !== nodeId);
              });
              
              return {
                ...prev,
                nodes: newNodes,
                selectedNodeId: prev.selectedNodeId === nodeId ? null : prev.selectedNodeId,
                isDirty: true
              };
            });
            
            saveLocalData();
          }
        }
      ]
    );
  }, []);

  /**
   * 保存到本地存储
   */
  const saveLocalData = async () => {
    try {
      const dataToSave = {
        nodes: editorState.nodes,
        lastSyncTime: Date.now(),
        version: '1.0'
      };
      
      await AsyncStorage.setItem(
        `behavior_tree_${entityId || 'default'}`,
        JSON.stringify(dataToSave)
      );
      
    } catch (error) {
      console.error('本地保存失败:', error);
    }
  };

  /**
   * 从本地存储加载
   */
  const loadLocalData = async () => {
    try {
      const data = await AsyncStorage.getItem(`behavior_tree_${entityId || 'default'}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('本地加载失败:', error);
      return null;
    }
  };

  /**
   * 同步到云端
   */
  const syncToCloud = async () => {
    if (!editorState.isOnline || !editorState.isDirty) return;
    
    try {
      if (onSave) {
        await onSave({
          nodes: editorState.nodes,
          timestamp: Date.now()
        });
        
        setEditorState(prev => ({
          ...prev,
          isDirty: false,
          lastSyncTime: Date.now()
        }));
      }
    } catch (error) {
      console.error('云同步失败:', error);
    }
  };

  /**
   * 获取节点类型名称
   */
  const getNodeTypeName = (type: BehaviorNodeType): string => {
    const names = {
      [BehaviorNodeType.SEQUENCE]: '顺序',
      [BehaviorNodeType.SELECTOR]: '选择',
      [BehaviorNodeType.PARALLEL]: '并行',
      [BehaviorNodeType.INVERTER]: '反转',
      [BehaviorNodeType.REPEATER]: '重复',
      [BehaviorNodeType.ACTION]: '动作',
      [BehaviorNodeType.CONDITION]: '条件',
      [BehaviorNodeType.WAIT]: '等待'
    };
    return names[type] || '未知';
  };

  /**
   * 获取节点颜色
   */
  const getNodeColor = (type: BehaviorNodeType): string => {
    const colors = {
      [BehaviorNodeType.SEQUENCE]: '#52c41a',
      [BehaviorNodeType.SELECTOR]: '#1890ff',
      [BehaviorNodeType.PARALLEL]: '#722ed1',
      [BehaviorNodeType.INVERTER]: '#fa8c16',
      [BehaviorNodeType.REPEATER]: '#eb2f96',
      [BehaviorNodeType.ACTION]: '#f5222d',
      [BehaviorNodeType.CONDITION]: '#faad14',
      [BehaviorNodeType.WAIT]: '#13c2c2'
    };
    return colors[type] || '#d9d9d9';
  };

  /**
   * 渲染节点
   */
  const renderNode = (node: MobileNodeConfig) => {
    const { position, size, isSelected } = node;
    const color = getNodeColor(node.type);
    
    return (
      <TapGestureHandler
        key={node.id}
        onHandlerStateChange={() => handleNodeTap(node.id)}
      >
        <View
          style={{
            position: 'absolute',
            left: position.x - size.width / 2,
            top: position.y - size.height / 2,
            width: size.width,
            height: size.height
          }}
        >
          <Svg width={size.width} height={size.height}>
            <Rect
              x={0}
              y={0}
              width={size.width}
              height={size.height}
              fill={color}
              stroke={isSelected ? '#000' : 'none'}
              strokeWidth={isSelected ? 2 : 0}
              rx={8}
            />
            <SvgText
              x={size.width / 2}
              y={size.height / 2}
              textAnchor="middle"
              alignmentBaseline="middle"
              fontSize={12}
              fill="#fff"
            >
              {node.name}
            </SvgText>
          </Svg>
        </View>
      </TapGestureHandler>
    );
  };

  /**
   * 渲染连接线
   */
  const renderConnections = () => {
    const connections: JSX.Element[] = [];
    
    Object.values(editorState.nodes).forEach(node => {
      node.children.forEach(childId => {
        const childNode = editorState.nodes[childId];
        if (childNode) {
          connections.push(
            <Line
              key={`${node.id}-${childId}`}
              x1={node.position.x}
              y1={node.position.y + node.size.height / 2}
              x2={childNode.position.x}
              y2={childNode.position.y - childNode.size.height / 2}
              stroke="#666"
              strokeWidth={2}
            />
          );
        }
      });
    });
    
    return connections;
  };

  /**
   * 渲染节点面板
   */
  const renderNodePalette = () => {
    const nodeTypes = [
      BehaviorNodeType.SEQUENCE,
      BehaviorNodeType.SELECTOR,
      BehaviorNodeType.PARALLEL,
      BehaviorNodeType.ACTION,
      BehaviorNodeType.CONDITION,
      BehaviorNodeType.WAIT
    ];
    
    return (
      <Modal visible={showNodePalette} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.nodePanel}>
            <Text style={styles.panelTitle}>添加节点</Text>
            <ScrollView>
              {nodeTypes.map(type => (
                <TouchableOpacity
                  key={type}
                  style={[styles.nodeButton, { backgroundColor: getNodeColor(type) }]}
                  onPress={() => {
                    addNode(type, { 
                      x: screenDimensions.width / 2, 
                      y: screenDimensions.height / 2 
                    });
                    setShowNodePalette(false);
                  }}
                >
                  <Text style={styles.nodeButtonText}>{getNodeTypeName(type)}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowNodePalette(false)}
            >
              <Text style={styles.closeButtonText}>关闭</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };

  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <View style={styles.toolbar}>
      <TouchableOpacity
        style={styles.toolButton}
        onPress={() => setShowNodePalette(true)}
      >
        <Text style={styles.toolButtonText}>+</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.toolButton}
        onPress={() => setShowProperties(true)}
      >
        <Text style={styles.toolButtonText}>⚙</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.toolButton, { opacity: editorState.isOnline ? 1 : 0.5 }]}
        onPress={syncToCloud}
        disabled={!editorState.isOnline}
      >
        <Text style={styles.toolButtonText}>☁</Text>
      </TouchableOpacity>
      
      <View style={styles.statusIndicator}>
        <View
          style={[
            styles.statusDot,
            { backgroundColor: editorState.isOnline ? '#52c41a' : '#f5222d' }
          ]}
        />
        <Text style={styles.statusText}>
          {editorState.isOnline ? '在线' : '离线'}
        </Text>
      </View>
    </View>
  );

  /**
   * 清理资源
   */
  const cleanup = () => {
    mobileEngine.dispose();
  };

  return (
    <View style={styles.container}>
      {renderToolbar()}
      
      <PinchGestureHandler
        ref={pinchRef}
        onGestureEvent={handlePinchGesture}
        onHandlerStateChange={handlePinchGesture}
      >
        <PanGestureHandler
          ref={panRef}
          onGestureEvent={handlePanGesture}
          onHandlerStateChange={handlePanGesture}
          simultaneousHandlers={pinchRef}
        >
          <View
            ref={canvasRef}
            style={[
              styles.canvas,
              {
                transform: [
                  { scale: gestureState.scale },
                  { translateX: gestureState.translateX },
                  { translateY: gestureState.translateY }
                ]
              }
            ]}
          >
            <Svg
              width={screenDimensions.width}
              height={screenDimensions.height}
              style={styles.svgCanvas}
            >
              {renderConnections()}
            </Svg>
            
            {Object.values(editorState.nodes).map(renderNode)}
          </View>
        </PanGestureHandler>
      </PinchGestureHandler>
      
      {renderNodePalette()}
    </View>
  );
};

/**
 * 样式定义
 */
const styles = {
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  toolbar: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e8e8e8'
  },
  toolButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1890ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10
  },
  toolButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold'
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto'
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5
  },
  statusText: {
    fontSize: 12,
    color: '#666'
  },
  canvas: {
    flex: 1,
    backgroundColor: '#fafafa'
  },
  svgCanvas: {
    position: 'absolute',
    top: 0,
    left: 0
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  nodePanel: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    width: '80%',
    maxHeight: '70%'
  },
  panelTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center'
  },
  nodeButton: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center'
  },
  nodeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold'
  },
  closeButton: {
    backgroundColor: '#f5222d',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold'
  }
};

export default MobileBehaviorEditor;
