const { execSync } = require('child_process');

console.log('开始检查TypeScript编译错误...');

try {
  // 直接运行TypeScript编译器
  const result = execSync('node_modules\\.bin\\tsc --noEmit --pretty', { 
    encoding: 'utf8',
    stdio: 'pipe'
  });
  
  console.log('编译成功，没有错误！');
  if (result) {
    console.log('输出:', result);
  }
} catch (error) {
  console.log('发现编译错误：');
  console.log('错误输出:', error.stdout || '');
  console.log('错误信息:', error.stderr || '');
  
  // 解析错误信息
  const output = error.stdout || error.stderr || '';
  const lines = output.split('\n');
  const errors = [];
  
  for (const line of lines) {
    if (line.includes('error TS')) {
      errors.push(line.trim());
    }
  }
  
  if (errors.length > 0) {
    console.log('\n具体错误列表:');
    errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
}
