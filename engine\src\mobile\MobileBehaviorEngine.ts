/**
 * 移动端轻量化行为引擎
 * 
 * 专为移动设备优化的行为引擎，具有以下特性：
 * - 内存使用优化
 * - 电池续航优化
 * - 网络带宽优化
 * - 触摸交互支持
 * - 设备传感器集成
 */

import { EventEmitter } from 'events';
import { 
  BehaviorTreeEngine, 
  BehaviorNode, 
  BehaviorNodeStatus,
  Blackboard 
} from '../ai/behavior/BehaviorTreeEngine';

/**
 * 移动设备信息
 */
export interface MobileDeviceInfo {
  platform: 'ios' | 'android' | 'web';
  screenSize: { width: number; height: number };
  pixelRatio: number;
  memoryLimit: number; // MB
  cpuCores: number;
  batteryLevel: number;
  networkType: 'wifi' | '4g' | '5g' | 'offline';
  orientation: 'portrait' | 'landscape';
  isLowPowerMode: boolean;
}

/**
 * 移动端配置
 */
export interface MobileEngineConfig {
  maxMemoryUsage: number;        // 最大内存使用 (MB)
  maxConcurrentTasks: number;    // 最大并发任务数
  updateFrequency: number;       // 更新频率 (Hz)
  enableBatteryOptimization: boolean;
  enableNetworkOptimization: boolean;
  enableTouchInput: boolean;
  enableSensorInput: boolean;
  qualityLevel: 'low' | 'medium' | 'high' | 'auto';
}

/**
 * 触摸事件
 */
export interface TouchEvent {
  type: 'start' | 'move' | 'end' | 'cancel';
  touches: Array<{
    id: number;
    x: number;
    y: number;
    pressure: number;
    timestamp: number;
  }>;
  timestamp: number;
}

/**
 * 传感器数据
 */
export interface SensorData {
  accelerometer: { x: number; y: number; z: number };
  gyroscope: { x: number; y: number; z: number };
  magnetometer: { x: number; y: number; z: number };
  orientation: { alpha: number; beta: number; gamma: number };
  timestamp: number;
}

/**
 * 性能监控
 */
export interface MobilePerformanceMetrics {
  memoryUsage: number;
  cpuUsage: number;
  batteryDrain: number;
  frameRate: number;
  networkUsage: number;
  thermalState: 'normal' | 'fair' | 'serious' | 'critical';
}

/**
 * 移动端轻量化行为引擎
 */
export class MobileBehaviorEngine extends BehaviorTreeEngine {
  private deviceInfo: MobileDeviceInfo;
  private config: MobileEngineConfig;
  private performanceMetrics: MobilePerformanceMetrics;

  // 优化组件
  private memoryPool: Map<string, any[]> = new Map();
  private taskQueue: Array<() => void> = [];
  private isProcessing = false;
  private lastUpdateTime = 0;

  // 输入处理
  private touchHandler?: (event: TouchEvent) => void;
  private sensorHandler?: (data: SensorData) => void;

  // 性能监控
  private performanceTimer?: NodeJS.Timeout;
  private frameCount = 0;
  private lastFrameTime = 0;

  // 移动端专用事件发射器
  private mobileEventEmitter = new EventEmitter();

  constructor(deviceInfo: MobileDeviceInfo, config: Partial<MobileEngineConfig> = {}) {
    super();
    
    this.deviceInfo = deviceInfo;
    this.config = {
      maxMemoryUsage: Math.min(deviceInfo.memoryLimit * 0.3, 128), // 最多使用30%内存
      maxConcurrentTasks: Math.min(deviceInfo.cpuCores * 2, 4),
      updateFrequency: deviceInfo.isLowPowerMode ? 30 : 60,
      enableBatteryOptimization: true,
      enableNetworkOptimization: true,
      enableTouchInput: true,
      enableSensorInput: false,
      qualityLevel: 'auto',
      ...config
    };
    
    this.initializeEngine();
  }

  /**
   * 初始化引擎
   */
  private initializeEngine(): void {
    // 初始化性能指标
    this.performanceMetrics = {
      memoryUsage: 0,
      cpuUsage: 0,
      batteryDrain: 0,
      frameRate: 0,
      networkUsage: 0,
      thermalState: 'normal'
    };
    
    // 初始化内存池
    this.initializeMemoryPool();
    
    // 设置输入处理
    if (this.config.enableTouchInput) {
      this.setupTouchInput();
    }
    
    if (this.config.enableSensorInput) {
      this.setupSensorInput();
    }
    
    // 启动性能监控
    this.startPerformanceMonitoring();
    
    // 根据设备自动调整质量
    if (this.config.qualityLevel === 'auto') {
      this.autoAdjustQuality();
    }
  }

  /**
   * 初始化内存池
   */
  private initializeMemoryPool(): void {
    // 预分配常用对象池
    this.memoryPool.set('vectors', []);
    this.memoryPool.set('matrices', []);
    this.memoryPool.set('events', []);
    this.memoryPool.set('tasks', []);
    
    // 预热对象池
    const vectorPool = this.memoryPool.get('vectors')!;
    for (let i = 0; i < 100; i++) {
      vectorPool.push({ x: 0, y: 0, z: 0 });
    }
    
    const eventPool = this.memoryPool.get('events')!;
    for (let i = 0; i < 50; i++) {
      eventPool.push({ type: '', data: null, timestamp: 0 });
    }
  }

  /**
   * 设置触摸输入
   */
  private setupTouchInput(): void {
    this.touchHandler = (event: TouchEvent) => {
      this.processTouchEvent(event);
    };
    
    // 在实际应用中，这里会绑定到DOM事件
    // document.addEventListener('touchstart', this.touchHandler);
    // document.addEventListener('touchmove', this.touchHandler);
    // document.addEventListener('touchend', this.touchHandler);
  }

  /**
   * 设置传感器输入
   */
  private setupSensorInput(): void {
    this.sensorHandler = (data: SensorData) => {
      this.processSensorData(data);
    };
    
    // 在实际应用中，这里会绑定到设备传感器API
    // if (DeviceMotionEvent) {
    //   window.addEventListener('devicemotion', this.sensorHandler);
    // }
  }

  /**
   * 优化的执行循环
   */
  public executeTreeOptimized(treeId: string, deltaTime: number): BehaviorNodeStatus | null {
    const now = performance.now();
    
    // 频率控制
    if (now - this.lastUpdateTime < 1000 / this.config.updateFrequency) {
      return null;
    }
    
    this.lastUpdateTime = now;
    
    // 电池优化
    if (this.config.enableBatteryOptimization && this.deviceInfo.batteryLevel < 0.2) {
      // 低电量模式：降低更新频率
      if (now - this.lastUpdateTime < 2000 / this.config.updateFrequency) {
        return null;
      }
    }
    
    // 内存检查
    if (this.performanceMetrics.memoryUsage > this.config.maxMemoryUsage) {
      this.performGarbageCollection();
    }
    
    // 执行行为树
    const result = super.executeTree(treeId, deltaTime);
    
    // 更新性能指标
    this.updateFrameRate();
    
    return result;
  }

  /**
   * 处理触摸事件
   */
  private processTouchEvent(event: TouchEvent): void {
    // 将触摸事件转换为行为树可理解的输入
    const touchData = {
      type: event.type,
      touchCount: event.touches.length,
      primaryTouch: event.touches[0] || null,
      timestamp: event.timestamp
    };
    
    // 更新黑板数据
    const blackboard = this.getSharedBlackboard();
    if (blackboard) {
      blackboard.set('touch_input', touchData);
      blackboard.set('last_touch_time', event.timestamp);
    }
    
    this.mobileEventEmitter.emit('touchEvent', touchData);
  }

  /**
   * 处理传感器数据
   */
  private processSensorData(data: SensorData): void {
    // 计算设备运动状态
    const acceleration = Math.sqrt(
      data.accelerometer.x ** 2 + 
      data.accelerometer.y ** 2 + 
      data.accelerometer.z ** 2
    );
    
    const rotation = Math.sqrt(
      data.gyroscope.x ** 2 + 
      data.gyroscope.y ** 2 + 
      data.gyroscope.z ** 2
    );
    
    const motionData = {
      acceleration,
      rotation,
      orientation: data.orientation,
      isMoving: acceleration > 1.0,
      isRotating: rotation > 0.1,
      timestamp: data.timestamp
    };
    
    // 更新黑板数据
    const blackboard = this.getSharedBlackboard();
    if (blackboard) {
      blackboard.set('device_motion', motionData);
      blackboard.set('device_orientation', data.orientation);
    }
    
    this.mobileEventEmitter.emit('sensorData', motionData);
  }

  /**
   * 自动调整质量
   */
  private autoAdjustQuality(): void {
    const deviceScore = this.calculateDeviceScore();
    
    if (deviceScore >= 0.8) {
      this.config.qualityLevel = 'high';
      this.config.updateFrequency = 60;
      this.config.maxConcurrentTasks = this.deviceInfo.cpuCores * 2;
    } else if (deviceScore >= 0.5) {
      this.config.qualityLevel = 'medium';
      this.config.updateFrequency = 30;
      this.config.maxConcurrentTasks = this.deviceInfo.cpuCores;
    } else {
      this.config.qualityLevel = 'low';
      this.config.updateFrequency = 15;
      this.config.maxConcurrentTasks = Math.max(1, this.deviceInfo.cpuCores / 2);
    }
  }

  /**
   * 计算设备性能评分
   */
  private calculateDeviceScore(): number {
    let score = 0;
    
    // CPU评分
    score += Math.min(this.deviceInfo.cpuCores / 8, 1) * 0.3;
    
    // 内存评分
    score += Math.min(this.deviceInfo.memoryLimit / 4096, 1) * 0.3;
    
    // 电池评分
    score += this.deviceInfo.batteryLevel * 0.2;
    
    // 网络评分
    const networkScore = this.deviceInfo.networkType === '5g' ? 1 :
                        this.deviceInfo.networkType === '4g' ? 0.7 :
                        this.deviceInfo.networkType === 'wifi' ? 0.9 : 0.3;
    score += networkScore * 0.2;
    
    return Math.min(score, 1);
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    this.performanceTimer = setInterval(() => {
      this.updatePerformanceMetrics();
    }, 1000);
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(): void {
    // 估算内存使用
    this.performanceMetrics.memoryUsage = this.estimateMemoryUsage();
    
    // 估算CPU使用
    this.performanceMetrics.cpuUsage = this.estimateCpuUsage();
    
    // 检查热状态
    this.performanceMetrics.thermalState = this.estimateThermalState();
    
    // 动态调整
    this.dynamicOptimization();
  }

  /**
   * 估算内存使用
   */
  private estimateMemoryUsage(): number {
    let usage = 0;
    
    // 统计对象池使用
    for (const [key, pool] of this.memoryPool) {
      usage += pool.length * this.getObjectSize(key);
    }
    
    // 统计行为树内存
    usage += this.getTreeCount() * 1024; // 每个树估算1KB
    
    // 统计黑板内存
    usage += this.getBlackboardCount() * 512; // 每个黑板估算512B
    
    return usage / (1024 * 1024); // 转换为MB
  }

  /**
   * 估算CPU使用
   */
  private estimateCpuUsage(): number {
    const activeTreeCount = this.getActiveTreeCount();
    const maxTrees = this.config.maxConcurrentTasks;
    
    return Math.min(activeTreeCount / maxTrees, 1);
  }

  /**
   * 估算热状态
   */
  private estimateThermalState(): 'normal' | 'fair' | 'serious' | 'critical' {
    const cpuUsage = this.performanceMetrics.cpuUsage;
    const frameRate = this.performanceMetrics.frameRate;
    const targetFrameRate = this.config.updateFrequency;
    
    if (cpuUsage > 0.9 || frameRate < targetFrameRate * 0.5) {
      return 'critical';
    } else if (cpuUsage > 0.7 || frameRate < targetFrameRate * 0.7) {
      return 'serious';
    } else if (cpuUsage > 0.5 || frameRate < targetFrameRate * 0.8) {
      return 'fair';
    } else {
      return 'normal';
    }
  }

  /**
   * 动态优化
   */
  private dynamicOptimization(): void {
    const metrics = this.performanceMetrics;
    
    // 根据热状态调整
    switch (metrics.thermalState) {
      case 'critical':
        this.config.updateFrequency = Math.max(10, this.config.updateFrequency * 0.5);
        this.config.maxConcurrentTasks = Math.max(1, Math.floor(this.config.maxConcurrentTasks * 0.5));
        break;
      case 'serious':
        this.config.updateFrequency = Math.max(15, this.config.updateFrequency * 0.7);
        this.config.maxConcurrentTasks = Math.max(1, Math.floor(this.config.maxConcurrentTasks * 0.7));
        break;
      case 'fair':
        this.config.updateFrequency = Math.max(20, this.config.updateFrequency * 0.9);
        break;
      case 'normal':
        // 逐渐恢复性能
        if (this.config.updateFrequency < 60) {
          this.config.updateFrequency = Math.min(60, this.config.updateFrequency * 1.1);
        }
        break;
    }
    
    // 内存压力处理
    if (metrics.memoryUsage > this.config.maxMemoryUsage * 0.8) {
      this.performGarbageCollection();
    }
  }

  /**
   * 执行垃圾回收
   */
  private performGarbageCollection(): void {
    // 清理对象池
    for (const [key, pool] of this.memoryPool) {
      if (pool.length > 100) {
        pool.splice(50); // 保留前50个对象
      }
    }
    
    // 清理过期的黑板数据
    this.cleanupBlackboards();
    
    // 清理任务队列
    this.taskQueue = this.taskQueue.slice(-10);
    
    this.mobileEventEmitter.emit('garbageCollected', {
      memoryFreed: this.estimateMemoryUsage(),
      timestamp: Date.now()
    });
  }

  /**
   * 更新帧率
   */
  private updateFrameRate(): void {
    const now = performance.now();
    this.frameCount++;
    
    if (now - this.lastFrameTime >= 1000) {
      this.performanceMetrics.frameRate = this.frameCount;
      this.frameCount = 0;
      this.lastFrameTime = now;
    }
  }

  /**
   * 获取对象大小估算
   */
  private getObjectSize(type: string): number {
    switch (type) {
      case 'vectors': return 24; // 3 * 8 bytes
      case 'matrices': return 128; // 4x4 * 8 bytes
      case 'events': return 64;
      case 'tasks': return 32;
      default: return 32;
    }
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(): MobilePerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * 获取设备信息
   */
  public getDeviceInfo(): MobileDeviceInfo {
    return { ...this.deviceInfo };
  }

  /**
   * 获取配置信息
   */
  public getConfig(): MobileEngineConfig {
    return { ...this.config };
  }

  /**
   * 更新设备信息
   */
  public updateDeviceInfo(info: Partial<MobileDeviceInfo>): void {
    this.deviceInfo = { ...this.deviceInfo, ...info };

    // 重新评估质量设置
    if (this.config.qualityLevel === 'auto') {
      this.autoAdjustQuality();
    }
  }

  /**
   * 监听移动端事件
   */
  public onMobileEvent(event: string, listener: (...args: any[]) => void): void {
    this.mobileEventEmitter.on(event, listener);
  }

  /**
   * 移除移动端事件监听器
   */
  public offMobileEvent(event: string, listener: (...args: any[]) => void): void {
    this.mobileEventEmitter.off(event, listener);
  }

  /**
   * 暂停引擎（应用进入后台时）
   */
  public pause(): void {
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
      this.performanceTimer = undefined;
    }
    
    this.mobileEventEmitter.emit('enginePaused');
  }

  /**
   * 恢复引擎（应用回到前台时）
   */
  public resume(): void {
    this.startPerformanceMonitoring();
    this.mobileEventEmitter.emit('engineResumed');
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    // 停止性能监控
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
    }

    // 清理输入处理器
    this.touchHandler = undefined;
    this.sensorHandler = undefined;

    // 清理内存池
    this.memoryPool.clear();
    this.taskQueue = [];

    // 清理父类资源（如果父类有dispose方法）
    const parentClass = Object.getPrototypeOf(Object.getPrototypeOf(this));
    if (parentClass && typeof parentClass.dispose === 'function') {
      parentClass.dispose.call(this);
    }

    this.mobileEventEmitter.emit('engineDisposed');
  }

  // 辅助方法的实际实现
  private getSharedBlackboard(): Blackboard | null {
    // 尝试获取第一个可用的黑板作为共享黑板
    const blackboards = (this as any).blackboards as Map<string, Blackboard> | undefined;
    if (blackboards && blackboards.size > 0) {
      return Array.from(blackboards.values())[0];
    }
    return null;
  }

  private getTreeCount(): number {
    // 返回当前行为树数量
    const trees = (this as any).trees as Map<string, any> | undefined;
    return trees?.size || 0;
  }

  private getBlackboardCount(): number {
    // 返回当前黑板数量
    const blackboards = (this as any).blackboards as Map<string, Blackboard> | undefined;
    return blackboards?.size || 0;
  }

  private getActiveTreeCount(): number {
    // 返回活跃行为树数量（简化为所有树都是活跃的）
    return this.getTreeCount();
  }

  private cleanupBlackboards(): void {
    // 清理过期的黑板数据
    const blackboards = (this as any).blackboards as Map<string, Blackboard> | undefined;
    if (blackboards) {
      for (const [, blackboard] of blackboards) {
        // 清理超过1小时未使用的数据
        const lastUsed = blackboard.get('last_used_time', 0);
        const now = Date.now();
        if (now - lastUsed > 3600000) { // 1小时
          this.clearBlackboardData(blackboard);
        }
      }
    }
  }

  private clearBlackboardData(blackboard: Blackboard): void {
    // 手动清理黑板数据的辅助方法
    try {
      const keys = Object.keys((blackboard as any).data || {});
      keys.forEach(key => blackboard.delete(key));
    } catch (error) {
      console.warn('清理黑板数据时出错:', error);
    }
  }
}
