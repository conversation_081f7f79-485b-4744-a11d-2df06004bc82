/**
 * EnhancedAvatarComponent 测试用例
 */
import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnhancedAvatarComponent, EmotionTypes, EnhancedAvatarConfig } from '../../src/avatar/components/EnhancedAvatarComponent';
import { Entity } from '../../src/core/Entity';

describe('EnhancedAvatarComponent', () => {
  let entity: Entity;
  let component: EnhancedAvatarComponent;

  beforeEach(() => {
    entity = new Entity('test-entity');
    component = new EnhancedAvatarComponent(entity);
  });

  afterEach(() => {
    component.dispose();
  });

  describe('基础功能测试', () => {
    test('应该正确初始化组件', () => {
      expect(component).toBeDefined();
      expect(component.getSessionId()).toBeDefined();
      expect(component.getIsSpeaking()).toBe(false);
      expect(component.getIsListening()).toBe(false);
    });

    test('应该正确设置组件类型', () => {
      expect(EnhancedAvatarComponent.TYPE).toBe('EnhancedAvatarComponent');
    });

    test('应该正确初始化默认配置', () => {
      const config = component.getConfig();
      expect(config.voice?.synthesis?.language).toBe('zh-CN');
      expect(config.enableSpeechRecognition).toBe(true);
      expect(config.enableSpeechSynthesis).toBe(true);
    });
  });

  describe('情感状态测试', () => {
    test('应该正确设置情感状态', () => {
      component.setEmotion(EmotionTypes.HAPPY, 0.8);
      const emotion = component.getCurrentEmotion();
      expect(emotion.type).toBe(EmotionTypes.HAPPY);
      expect(emotion.intensity).toBe(0.8);
    });

    test('应该限制情感强度在有效范围内', () => {
      component.setEmotion(EmotionTypes.SAD, 1.5);
      expect(component.getCurrentEmotion().intensity).toBe(1.0);

      component.setEmotion(EmotionTypes.ANGRY, -0.5);
      expect(component.getCurrentEmotion().intensity).toBe(0.0);
    });

    test('应该处理无效的情感类型', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      component.setEmotion('', 0.5);
      expect(consoleSpy).toHaveBeenCalledWith('无效的情感类型:', '');
      consoleSpy.mockRestore();
    });
  });

  describe('知识库管理测试', () => {
    test('应该正确设置知识库ID', () => {
      const knowledgeBaseId = 'test-kb-123';
      component.setKnowledgeBase(knowledgeBaseId);
      expect(component.getKnowledgeBaseId()).toBe(knowledgeBaseId);
    });

    test('应该处理未初始化的知识库配置', () => {
      const componentWithoutKB = new EnhancedAvatarComponent(entity, {
        knowledgeBase: undefined
      });
      
      componentWithoutKB.setKnowledgeBase('test-kb');
      expect(componentWithoutKB.getKnowledgeBaseId()).toBe('test-kb');
      
      componentWithoutKB.dispose();
    });
  });

  describe('配置更新测试', () => {
    test('应该正确更新配置', () => {
      const newConfig: Partial<EnhancedAvatarConfig> = {
        enableSpeechRecognition: false,
        voice: {
          synthesis: {
            language: 'en-US',
            rate: 1.2
          }
        }
      };

      component.updateConfig(newConfig);
      const config = component.getConfig();
      
      expect(config.enableSpeechRecognition).toBe(false);
      expect(config.voice?.synthesis?.language).toBe('en-US');
      expect(config.voice?.synthesis?.rate).toBe(1.2);
      // 确保其他配置没有被覆盖
      expect(config.voice?.synthesis?.voice).toBe('zh-CN-XiaoxiaoNeural');
    });

    test('应该处理无效的配置输入', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      component.updateConfig(null as any);
      expect(consoleSpy).toHaveBeenCalledWith('无效的配置对象:', null);
      consoleSpy.mockRestore();
    });
  });

  describe('会话管理测试', () => {
    test('应该正确生成会话ID', () => {
      const sessionId1 = component.getSessionId();
      component.restartSession();
      const sessionId2 = component.getSessionId();
      
      expect(sessionId1).not.toBe(sessionId2);
      expect(sessionId1).toMatch(/^session_\d+_[a-z0-9]+$/);
      expect(sessionId2).toMatch(/^session_\d+_[a-z0-9]+$/);
    });

    test('应该正确管理对话历史', () => {
      expect(component.getConversationHistory()).toHaveLength(0);
      
      component.clearConversationHistory();
      expect(component.getConversationHistory()).toHaveLength(0);
    });
  });

  describe('语音交互测试', () => {
    test('应该正确启动和停止语音交互', async () => {
      await component.startVoiceInteraction();
      expect(component.getIsListening()).toBe(true);
      
      component.stopVoiceInteraction();
      expect(component.getIsListening()).toBe(false);
    });

    test('应该在语音识别未启用时抛出错误', async () => {
      component.updateConfig({ enableSpeechRecognition: false });
      
      await expect(component.startVoiceInteraction()).rejects.toThrow('语音识别未启用');
    });

    test('应该正确处理语音回复', async () => {
      const text = '测试回复';
      await component.speakResponse(text);
      
      const history = component.getConversationHistory();
      expect(history).toHaveLength(1);
      expect(history[0].content).toBe(text);
      expect(history[0].role).toBe('assistant');
    });
  });

  describe('事件处理测试', () => {
    test('应该正确添加和移除事件监听器', () => {
      const listener = vi.fn();

      component.addEventListener('test-event', listener);
      component.removeEventListener('test-event', listener);

      // 这里无法直接测试事件发射，但可以确保方法不会抛出错误
      expect(listener).not.toHaveBeenCalled();
    });
  });

  describe('销毁测试', () => {
    test('应该正确销毁组件', () => {
      component.startVoiceInteraction();
      expect(component.getIsListening()).toBe(true);
      
      component.dispose();
      expect(component.getIsListening()).toBe(false);
    });
  });
});
